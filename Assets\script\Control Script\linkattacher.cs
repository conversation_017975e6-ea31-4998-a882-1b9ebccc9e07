using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

public class linkattacher : MonoBehaviour
{
    [Header("Active Equipment")]
    [Space(10)]
    public GameObject grasscutter, groundflater, grasspick, saeedmachine, plough;

    [Space(20)]
    [Header("Inactive Equipment")]
    [Space(10)]
    public GameObject grasscutter1, groundflater1, grasspick1, saeedmachine1, plough1;

    [Space(20)]
    [Header("Equipment Check Arrays")]
    [Space(10)]
    public GameObject[] firstcheck, scondcheck;
    public Transform TractorPosition;

    [Space(20)]
    [Header("Fade Screen")]
    [Space(10)]
    public Image fadeScreen; // Assign a UI Image for fade effect
    public float fadeDuration = 0.5f;
    public Transform tractorTransform;
 // Reference to the tractor GameObject
     void Start()
    {
       
    }
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.CompareTag("Grasscutter"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, grasscutter, grasscutter1));
        }
        else if (other.gameObject.CompareTag("Groundflater"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, groundflater, groundflater1));
        }
        else if (other.gameObject.CompareTag("Grasspicker"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, grasspick, grasspick1));
        }
        else if (other.gameObject.CompareTag("Saeedmachine"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, saeedmachine, saeedmachine1));
        }
        else if (other.gameObject.CompareTag("Plough"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, plough, plough1));
        }
    }

    IEnumerator HandleEquipmentChange(Collider other, GameObject activeEquipment, GameObject inactiveEquipment)
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            this.GetComponent<Rigidbody>().drag = 0.01f;
        }

        // Reset tractor position
        if (tractorTransform != null && TractorPosition != null)
        {
            tractorTransform.position = TractorPosition.position;
            tractorTransform.rotation = TractorPosition.rotation;
        }

        // Equipment changes
        activeEquipment.SetActive(true);
        inactiveEquipment.SetActive(false);
        other.gameObject.SetActive(false);

        // Handle check arrays
        foreach (GameObject obj in firstcheck)
        {
            obj.SetActive(false);
        }
        foreach (GameObject obj in scondcheck)
        {
            obj.SetActive(true);
        }

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            RCC_Camera.instance.TPSDistance = 28;
            RCC_Camera.instance.TPSHeight = 6;
            fadeScreen.gameObject.SetActive(false);
        }
    }
}
