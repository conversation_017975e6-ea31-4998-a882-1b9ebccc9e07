using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class spraycontrol : MonoBehaviour
{
    public GameObject SmallPlantPrefab;
    
    [Header("Progress System")]
    public Slider progressSlider;
    public Text progressText;
    public GameObject completePanel;
    
    [Header("Settings")]
    public int maxTriggers = 500;
    
    private int currentTriggers = 0;
    void Start()
    {
        // Initialize UI
        UpdateProgressUI();
        
        // Make sure complete panel is initially hidden
        if (completePanel != null)
            completePanel.SetActive(false);
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "spray")
        {
            // Spawn small plant at the spray position
            if (SmallPlantPrefab != null)
            {
                Instantiate(SmallPlantPrefab, other.transform.position, other.transform.rotation);
            }
            
            // Increment trigger count
            currentTriggers++;
            
            // Update progress UI
            UpdateProgressUI();
            
            // Check if we've reached the maximum triggers
            if (currentTriggers >= maxTriggers)
            {
                ShowCompletePanel();
            }
            
            other.gameObject.SetActive(false);
        }
    }
    
    void UpdateProgressUI()
    {
        float progress = (float)currentTriggers / maxTriggers;
        
        // Update slider
        if (progressSlider != null)
        {
            progressSlider.value = progress;
        }
        
        // Update text with percentage
        if (progressText != null)
        {
            int percentage = Mathf.RoundToInt(progress * 100f);
            progressText.text = percentage + "%";
        }
    }
    
    void ShowCompletePanel()
    {
        if (completePanel != null)
        {
            completePanel.SetActive(true);
        }
        
        Debug.Log("Farming Complete! 500 triggers reached!");
    }
    
    // Optional: Reset progress method
    public void ResetProgress()
    {
        currentTriggers = 0;
        UpdateProgressUI();
        
        if (completePanel != null)
            completePanel.SetActive(false);
    }
}
