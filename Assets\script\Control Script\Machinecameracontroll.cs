using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Machinecameracontroll : MonoBehaviour
{
    public RCC_Camera controlcam;
    public float hight;
    public float distance;
    public void Start()
    {
        controlcam = GetComponent<RCC_Camera>();
    }
    public void Update()
    {
        controlcam.TPSHeight = hight;
        controlcam.TPSDistance = distance;
    }
}
