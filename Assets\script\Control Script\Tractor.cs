using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

public class Tractor : MonoBehaviour
{
    public Transform Seed, trailer;

    [Header("Fade Screen")]
    public Image fadeScreen; // Assign a UI Image for fade effect
    public float fadeDuration = 0.5f;
    public GameObject[] firstcheck,lastcheck;
    private bool isCheckPointActive = true;
    public GameObject cutscene,rcccam,canvas,complete;
    public void OnTriggerEnter(Collider collision)
    {
        if (collision.gameObject.tag == "checkpoint")
        {
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            collision.gameObject.transform.GetChild(0).gameObject.SetActive(false);
            collision.gameObject.transform.GetChild(1).gameObject.SetActive(true);
        }
        else if (collision.gameObject.tag == "finishline")
        {
            collision.gameObject.SetActive(false);
            Tructorlinkcontroll.instance.SmoothLinkDown();
        }
        else if (collision.gameObject.tag == "linkSeedM")
        {
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in lastcheck)
            {
                obj.SetActive(true);
            }
            collision.gameObject.SetActive(false);
            StartCoroutine(ResetTractorPosition());
        }
        else if (collision.gameObject.tag == "trailerlink")
        {
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in lastcheck)
            {
                obj.SetActive(true);
            }

            collision.gameObject.SetActive(false);
            StartCoroutine(ResetTractorPosition1());
        }
        else if (collision.gameObject.tag == "finish")
        {
            StartCoroutine(HandleFinishWithFade(collision));
             collision.gameObject.SetActive(false);
        }

    }

    IEnumerator ResetTractorPosition()
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        yield return new WaitForSeconds(0.5f);
        this.transform.position = Seed.position;
        this.transform.rotation = Seed.rotation;

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            fadeScreen.gameObject.SetActive(false);
        }
    }

    IEnumerator ResetTractorPosition1()
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        yield return new WaitForSeconds(0.5f);
        this.transform.position = trailer.position;
        this.transform.rotation = trailer.rotation;

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            fadeScreen.gameObject.SetActive(false);
        }
    }

    IEnumerator HandleFinishWithFade(Collider collision)
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        // Wait a moment while screen is black
        yield return new WaitForSeconds(0.2f);

        // Activate finish sequence (without complete panel)
        cutscene.SetActive(true);
        rcccam.SetActive(false);
        canvas.SetActive(false);

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            fadeScreen.gameObject.SetActive(false);
        }

        // Wait 6 seconds then activate complete panel
        yield return new WaitForSeconds(10f);
        complete.SetActive(true);
    }

}
